import pandas as pd
from sklearn.preprocessing import StandardScaler, OneHotEncoder

def preprocess_data(filepath):
    df = pd.read_csv(filepath)
    
    # Handle missing values in the dataset
    df = df.fillna('missing')

    if 'Label' not in df.columns:
        raise ValueError("Label column not found in dataset")

    X = df.drop(['Label'], axis=1)
    y = df['Label']

    # Ensure all categorical columns are encoded, including protocol_type, service, and flag
    categorical_columns = X.select_dtypes(include=['object']).columns

    if not categorical_columns.empty:
        encoder = OneHotEncoder(sparse=False, handle_unknown='ignore')
        encoded_features = encoder.fit_transform(X[categorical_columns])
        encoded_df = pd.DataFrame(encoded_features, columns=encoder.get_feature_names_out(categorical_columns))
        X = X.drop(categorical_columns, axis=1).reset_index(drop=True)
        X = pd.concat([X, encoded_df], axis=1)

    # Explicitly convert all columns to numeric where possible
    for column in X.columns:
        X[column] = pd.to_numeric(X[column], errors='coerce')

    # Drop rows with NaN values after conversion
    X = X.dropna().reset_index(drop=True)

    # Scale numeric features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    return X_scaled, y
