import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
from detect import detect_threat
from train import *

st.set_page_config(page_title="APT Attack Detection", layout="wide")
st.title("🔐 AI-Powered APT Attack Detection System")

menu = ["Upload New Data", "Train Model"]
choice = st.sidebar.selectbox("Menu", menu)

if choice == "Upload New Data":
    st.subheader("Detect APT Attacks from Log File")
    uploaded_file = st.file_uploader("Upload CSV file", type="csv")
    if uploaded_file is not None:
        data = pd.read_csv(uploaded_file)
        st.dataframe(data.head())
        data.to_csv("data/temp_input.csv", index=False)
        if st.button("Run Detection"):
            alerts, predictions, df = detect_threat("data/temp_input.csv")
            df['Prediction'] = predictions
            if alerts:
                st.error("\n".join(alerts))
            else:
                st.success("No APT activity detected.")

            st.subheader("📊 Detection Results Visualization")
            counts = df['Prediction'].value_counts()
            fig1 = px.pie(values=counts.values, names=['Normal' if val==0 else 'APT Attack' for val in counts.index],
                          title='APT vs Normal Traffic')
            st.plotly_chart(fig1)

            fig2 = px.histogram(df, x='src_bytes', color='Prediction', nbins=30,
                                labels={'src_bytes': 'Source Bytes'},
                                title='Distribution of Source Bytes')
            st.plotly_chart(fig2)

            if 'dst_bytes' in df.columns and 'src_bytes' in df.columns:
                fig3 = px.scatter(df, x='src_bytes', y='dst_bytes', color='Prediction',
                                  title='Source Bytes vs Destination Bytes')
                st.plotly_chart(fig3)

elif choice == "Train Model":
    st.subheader("Train APT Detection Model")
    if st.button("Train Now"):
        X, y = preprocess_data('data/apt_logs.csv')
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X, y)
        joblib.dump(model, 'models/apt_model.pkl')
        st.success("Model trained and saved successfully.")
