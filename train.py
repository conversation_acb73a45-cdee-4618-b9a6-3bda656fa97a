from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report
import joblib
from preprocess import preprocess_data

# Load and preprocess data
X, y = preprocess_data('data/apt_logs.csv')

# Train model
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X, y)

# Evaluate
print("Model Evaluation:\n")
print(classification_report(y, model.predict(X)))

# Save model
joblib.dump(model, 'models/apt_model.pkl')
