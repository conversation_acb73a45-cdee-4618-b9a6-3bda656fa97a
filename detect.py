import joblib
import pandas as pd
from sklearn.preprocessing import StandardScaler

def detect_threat(new_data_path):
    model = joblib.load('models/apt_model.pkl')
    df = pd.read_csv(new_data_path)
    df = df.dropna()

    scaler = StandardScaler()
    X = scaler.fit_transform(df)

    predictions = model.predict(X)
    results = []
    for i, pred in enumerate(predictions):
        if pred == 1:
            results.append(f"[ALERT] Potential APT Activity Detected at row {i}")
    return results, predictions, df

if __name__ == "__main__":
    alerts, _, _ = detect_threat('data/new_logs.csv')
    for alert in alerts:
        print(alert)
